<?php

namespace App\Http\Controllers;

use App\Models\GameSession;
use App\Models\GameSessionQuestion;
use App\Models\User;
use App\Services\GameService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GameController extends Controller
{
    public function __construct(protected GameService $gameService) {}

    private function getLanguage(Request $request): string
    {
        return $request->query('lang', 'en'); // default to English
    }

    private function pickLang($field, ?string $lang)
    {
        if (is_string($field)) {
            $field = json_decode($field, true);
        }
        if (is_array($field) && $lang && isset($field[$lang])) {
            return $field[$lang];
        }
        return is_array($field) ? reset($field) : $field; // fallback to first value
    }

    private function formatChoicesFromQuestion($question, ?string $lang = 'en'): array
    {
        $decodeField = fn($field) => is_string($field) ? json_decode($field, true) : $field;

        $answerA = $decodeField($question->answer_a);
        $answerB = $decodeField($question->answer_b);
        $answerC = $decodeField($question->answer_c);
        $answerD = $decodeField($question->answer_d);

        return [
            ['id' => 'A', 'text' => $this->pickLang($answerA, $lang)],
            ['id' => 'B', 'text' => $this->pickLang($answerB, $lang)],
            ['id' => 'C', 'text' => $this->pickLang($answerC, $lang)],
            ['id' => 'D', 'text' => $this->pickLang($answerD, $lang)],
        ];
    }

    // POST /api/game/start
    public function start(Request $request): JsonResponse
    {
        $lang = $this->getLanguage($request);
        $user = $request->user();
        $session = $this->gameService->startForUser($user);

        $current = $session->questions->first();

        return response()->json([
            'session_id' => $session->id,
            'question' => $current ? [
                'session_question_id' => $current->id,
                'question_id' => $current->question->id,
                'category' => $this->pickLang($current->question->category->name, $lang),
                'text' => $this->pickLang($current->question->question_text, $lang),
                'choices' => $this->formatChoicesFromQuestion($current->question, $lang),
                'level' => $current->question->level,
                'points' => $current->question->points,
                'image' => $current->question->image ?? null,
            ] : null,
        ]);
    }

    // POST /api/game/answer
    public function answer(Request $request): JsonResponse
    {
        $lang = $this->getLanguage($request);
        $data = $request->validate([
            'session_id' => 'required|exists:game_sessions,id',
            'session_question_id' => 'required|exists:game_session_questions,id',
            'selected_answer' => 'required|string|in:A,B,C,D',
            'time_taken' => 'nullable|integer|min:0',
        ]);

        $session = GameSession::findOrFail($data['session_id']);
        if ($session->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $ssq = GameSessionQuestion::findOrFail($data['session_question_id']);
        $result = $this->gameService->submitAnswer($session, $ssq, $data['selected_answer'] ?? null, $data['time_taken'] ?? null);

        return response()->json([
            'score' => $result['session']->score,
            'questions_answered' => $result['session']->questions()->where('answered', true)->count(),
            'remaining_question' => $result['session']->questions()->whereNull('selected_answer')->count(),
            'total_questions' => $result['session']->questions()->count(),
            'hints_used' => $result['session']->hints_used,
            'time_taken' => $ssq->time_taken,
            'correct' => $result['correct'],
            'next_question' => $result['next'] ? [
                'session_question_id' => $result['next']->id,
                'question_id' => $result['next']->question->id,
                'text' => $this->pickLang($result['next']->question->question_text, $lang),
                'category' => $this->pickLang($result['next']->question->category->name, $lang),
                'choices' => $this->formatChoicesFromQuestion($result['next']->question, $lang),
                'level' => $result['next']->question->level,
                'points' => $result['next']->question->points,
                'image' => $result['next']->question->image,
            ] : null,
            'finished' => !$result['next'],
        ]);
    }

    // POST /api/game/hint
    public function hint(Request $request): JsonResponse
    {
        $data = $request->validate([
            'session_id' => 'required|exists:game_sessions,id',
            'session_question_id' => 'required|exists:game_session_questions,id',
        ]);

        $session = GameSession::findOrFail($data['session_id']);
        if ($session->user_id !== $request->user()->id) return response()->json(['message' => 'Unauthorized'], 403);

        $ssq = GameSessionQuestion::findOrFail($data['session_question_id']);

        try {
            $hint = $this->gameService->giveHint($session, $ssq);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 422);
        }

        return response()->json(['hint' => $hint, 'hints_used' => $session->fresh()->hints_used]);
    }

    // GET /api/game/score/{session}
    public function score(GameSession $session): JsonResponse
    {
        try {
            $user = Auth::user();
            if ($session->user_id !== $user->id) return response()->json(['message' => 'Unauthorized'], 403);

            return response()->json([
                'score' => $session->score,
                'correct_answers' => $session->questions()->where('is_correct', true)->count(),
                'incorrect_answers' => $session->questions()->where('is_correct', false)->count(),
                'time_over_count' => $session->questions()->whereNull('selected_answer')->count(),
                'questions_answered' => $session->questions()->where('answered', true)->count(),
                'total_questions' => $session->questions()->count(),
                'hints_used' => $session->hints_used,
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Session not found'], 404);
        }
    }

    // POST /api/game/continue
    public function playAgain(Request $request): JsonResponse
    {
        $lang = $this->getLanguage($request);
        $data = $request->validate(['session_id' => 'nullable|exists:game_sessions,id']);
        $user = $request->user();

        $session = $this->gameService->startForUser($user);

        $current = $session->questions->first();

        return response()->json([
            'session_id' => $session->id,
            'question' => $current ? [
                'session_question_id' => $current->id,
                'question_id' => $current->question->id,
                'text' => $this->pickLang($current->question->question_text, $lang),
                'category' => $this->pickLang($current->question->category->name, $lang),
                'choices' => $this->formatChoicesFromQuestion($current->question, $lang),
                'level' => $current->question->level,
                'points' => $current->question->points,
                'image' => $current->question->image,
            ] : null,
            'time_limit' => GameService::TIME_LIMIT_SECONDS
        ]);
    }

    public function leaderboard(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate   = $request->input('end_date');

        $leaders = User::select('id', 'full_name')
            ->withMax(['gameSessions as total_score' => function ($query) use ($startDate, $endDate) {
                if ($startDate) {
                    $query->whereDate('created_at', '>=', $startDate);
                }
                if ($endDate) {
                    $query->whereDate('created_at', '<=', $endDate);
                }
            }], 'score')
            ->withMin(['gameSessionQuestions as total_time_taken' => function ($query) use ($startDate, $endDate) {
                $query->whereHas('session', function ($q) use ($startDate, $endDate) {
                    if ($startDate) {
                        $q->whereDate('created_at', '>=', $startDate);
                    }
                    if ($endDate) {
                        $q->whereDate('created_at', '<=', $endDate);
                    }
                });
            }], 'time_taken')
            ->orderBy('total_score', 'DESC')
            ->paginate(10);

        return response()->json($leaders);
    }

    // POST /api/game/timeout
    public function timeout(Request $request): JsonResponse
    {
        $lang = $this->getLanguage($request);
        $data = $request->validate([
            'session_id' => 'required|exists:game_sessions,id',
            'session_question_id' => 'required|exists:game_session_questions,id',
        ]);

        $session = GameSession::findOrFail($data['session_id']);
        if ($session->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $ssq = GameSessionQuestion::findOrFail($data['session_question_id']);
        $result = $this->gameService->handleTimeout($session, $ssq);

        $timeRemaining = 0;
        if ($result['next'] && $result['next']->question_start_time) {
            $elapsed = now()->diffInSeconds($result['next']->question_start_time);
            $timeRemaining = max(0, GameService::TIME_LIMIT_SECONDS - $elapsed);
        }

        return response()->json([
            'score' => $result['session']->score,
            'questions_answered' => $result['session']->questions()->where('answered', true)->count(),
            'time_over_count' => $result['session']->questions()->whereNull('selected_answer')->count(),
            'time_remaining' => $timeRemaining,
            'total_questions' => $result['session']->questions()->count(),
            'hints_used' => $result['session']->hints_used,
            'correct' => false,
            'next_question' => $result['next'] ? [
                'session_question_id' => $result['next']->id,
                'question_id' => $result['next']->question->id,
                'text' => $this->pickLang($result['next']->question->question_text, $lang),
                'category' => $this->pickLang($result['next']->question->category->name, $lang),
                'choices' => $this->formatChoicesFromQuestion($result['next']->question, $lang),
                'level' => $result['next']->question->level,
                'points' => $result['next']->question->points,
                'image' => $result['next']->question->image,
            ] : null,
            'finished' => !$result['next'],
        ]);
    }

    // POST /api/game/navigate
    public function navigate(Request $request): JsonResponse
    {
        $lang = $this->getLanguage($request);
        $data = $request->validate([
            'session_id' => 'required|exists:game_sessions,id',
            'direction' => 'required|string|in:next,previous',
        ]);

        $session = GameSession::findOrFail($data['session_id']);
        if ($session->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $result = $this->gameService->navigateQuestion($session, $data['direction']);

        return response()->json([
            'session_id' => $session->id,
            'score' => $session->score,
            'questions_answered' => $session->questions()->where('answered', true)->count(),
            'time_over_count' => $session->questions()->whereNull('selected_answer')->count(),
            'time_remaining' => $result['time_remaining'],
            'total_questions' => $session->questions()->count(),
            'hints_used' => $session->hints_used,
            'question' => $result['question'] ? [
                'session_question_id' => $result['question']->id,
                'question_id' => $result['question']->question->id,
                'category' => $this->pickLang($result['question']->question->category->name, $lang),
                'text' => $this->pickLang($result['question']->question->question_text, $lang),
                'choices' => $this->formatChoicesFromQuestion($result['question']->question, $lang),
                'level' => $result['question']->question->level,
                'points' => $result['question']->question->points,
                'image' => $result['question']->question->image,
            ] : null,
            'finished' => !$result['question'],
        ]);
    }
}
