<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

    protected function prepareForValidation()
    {
        if ($this->has('status')) {
            // Convert "true"/"false", 1/0 strings to actual booleans
            $this->merge([
                'status' => filter_var($this->status, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
            ]);
        }
    }

    public function rules(): array
    {
        $user = Auth::user();

        $rules = [
            'question_category_id' => 'sometimes|required|exists:question_categories,id',
            'correct_answer' => 'sometimes|required|in:A,B,C,D',

            'question_text' => 'sometimes|required|array',
            'question_text.en' => 'nullable|string|max:1000',
            'question_text.am' => 'nullable|string|max:1000',
            'question_text.om' => 'nullable|string|max:1000',
            'question_text.ti' => 'nullable|string|max:1000',

            'answer_a' => 'sometimes|required|array',
            'answer_a.en' => 'nullable|string|max:255',
            'answer_a.am' => 'nullable|string|max:255',
            'answer_a.om' => 'nullable|string|max:255',
            'answer_a.ti' => 'nullable|string|max:255',

            'answer_b' => 'sometimes|required|array',
            'answer_b.en' => 'nullable|string|max:255',
            'answer_b.am' => 'nullable|string|max:255',
            'answer_b.om' => 'nullable|string|max:255',
            'answer_b.ti' => 'nullable|string|max:255',

            'answer_c' => 'sometimes|required|array',
            'answer_c.en' => 'nullable|string|max:255',
            'answer_c.am' => 'nullable|string|max:255',
            'answer_c.om' => 'nullable|string|max:255',
            'answer_c.ti' => 'nullable|string|max:255',

            'answer_d' => 'sometimes|required|array',
            'answer_d.en' => 'nullable|string|max:255',
            'answer_d.am' => 'nullable|string|max:255',
            'answer_d.om' => 'nullable|string|max:255',
            'answer_d.ti' => 'nullable|string|max:255',
        ];

        if ($user && $user->hasRole('super_admin')) {
            $rules['level']  = 'sometimes|required|in:1,2,3,4,5';
            $rules['points'] = 'sometimes|required|integer|min:1';
            $rules['status'] = 'sometimes|boolean';
            $rules['hint']   = 'sometimes|required|array';
            $rules['hint.en'] = 'nullable|string|max:500';
            $rules['hint.am'] = 'nullable|string|max:500';
            $rules['hint.om'] = 'nullable|string|max:500';
            $rules['hint.ti'] = 'nullable|string|max:500';
        } elseif ($user && $user->hasRole('admin')) {
            $rules['level']  = 'sometimes|nullable|in:1,2,3,4,5';
            $rules['points'] = 'sometimes|nullable|integer';
            $rules['hint']   = 'sometimes|nullable|array';
            $rules['hint.en'] = 'nullable|string|max:500';
            $rules['hint.am'] = 'nullable|string|max:500';
            $rules['hint.om'] = 'nullable|string|max:500';
            $rules['hint.ti'] = 'nullable|string|max:500';
        }

        return $rules;
    }
}
