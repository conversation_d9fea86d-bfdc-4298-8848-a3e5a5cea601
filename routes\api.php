<?php

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BudgetController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ExpenseUserController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\GoalController;
use App\Http\Controllers\IncomeController;
use App\Http\Controllers\IncomeUserController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OTPController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\QuestionCategoryController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\RegularPaymentController;
use App\Http\Controllers\ResetPasswordController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::post('unsubscribe', [AuthController::class, 'unsubscribe']);
Route::group([
    'middleware' => 'api',
], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);


    /**
     *  Other AuthController routes
     */
    Route::post('create-password', [AuthController::class, 'create_password'])->middleware('auth:api');
    Route::post('update-profile-image', [AuthController::class, 'update_profile_image'])->middleware('auth:api');
    Route::post('remove-profile-image', [AuthController::class, 'remove_profile_image'])->middleware('auth:api');

    Route::post('update-profile', [AuthController::class, 'update_profile'])->middleware('auth:api');

    Route::post('reset-password', [ResetPasswordController::class, 'reset_password']);
    Route::post('change-password', [ResetPasswordController::class, 'change_password'])->middleware('auth:api');

    Route::post('verify-otp', [OTPController::class, 'verify_otp']);
    Route::post('resend-otp', [OTPController::class, 'resend_otp']);

    Route::group([
        'middleware' => 'auth:api',
    ], function () {

        /**
         * Common Endpoints */
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
        Route::get('/my-profile', [AuthController::class, 'profile']);

        Route::get('my-activity-logs', [ActivityLogController::class, 'myActivityLog']);


        Route::post('test-notification', [NotificationController::class, 'testNotification']);

        Route::get('my-notifications', [NotificationController::class, 'getNotification']);
        Route::post('read-all-notifications', [NotificationController::class, 'readNotifications']);
        Route::post('read-notification/{id}', [NotificationController::class, 'readNotification']);


        /**
         * Super Admin Endpoints */
        Route::resource('roles', RoleController::class);
        Route::get('permissions', [PermissionController::class, 'index']);
        Route::get('users', [UserController::class, 'index']);
        Route::get('users/{user}', [UserController::class, 'show']);
        Route::post('users/{user}', [UserController::class, 'update']);


        Route::post('roles/{role}/attach-permissions', [RolePermissionController::class, 'attachPermission']);
        Route::post('roles/{role}/detach-permissions', [RolePermissionController::class, 'detachPermission']);
        Route::post('assign-role/{user}', [RoleController::class, 'assignRole']);

        Route::get('get-activity-logs', [ActivityLogController::class, 'index']);

        /**
         * Admin Endpoints
         * */
        Route::apiResource('question-categories', QuestionCategoryController::class);
        Route::resource('questions', QuestionController::class);

        /**
         * Game Endpoints */

        Route::post('game/start', [GameController::class, 'start']);
        Route::post('game/answer', [GameController::class, 'answer']);
        Route::post('game/hint', [GameController::class, 'hint']);
        Route::get('game/score/{session}', [GameController::class, 'score']);
        Route::post('game/continue', [GameController::class, 'playAgain']);
        Route::get('game/leaderboard', [GameController::class, 'leaderboard']);
        Route::post('game/timeout', [GameController::class, 'timeout']);
        Route::post('game/navigate', [GameController::class, 'navigate']);
    });
});
