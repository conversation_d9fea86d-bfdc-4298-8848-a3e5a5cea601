<?php

namespace App\Providers;

use App\Models\Question;
use App\Models\QuestionCategory;
use App\Policies\QuestionCategoryPolicy;
use App\Policies\QuestionPolicy;
use Illuminate\Support\ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    protected $policies = [
        Question::class => QuestionPolicy::class,
        QuestionCategory::class => QuestionCategoryPolicy::class,
    ];

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
