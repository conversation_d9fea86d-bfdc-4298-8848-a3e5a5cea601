pipeline {
    agent any
    
    stages {

        stage("Pull from gitlab") {
          steps {
               checkout scmGit(branches: [[name: '*/master']], extensions: [], userRemoteConfigs: [[credentialsId: 'alet-onevas-github', url: 'http://gitlab.local/adane.woris/spotaddis.git']])
          }
        }

       stage("build docker image") {
            steps {
                sh "sudo docker build -t *************:5000/spotaddis ."
            }
        }

        stage("push to local registry") {
            steps {
                sh "sudo docker push *************:5000/spotaddis"
            }
        }



    }
}