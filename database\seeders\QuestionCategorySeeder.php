<?php

namespace Database\Seeders;

use App\Models\QuestionCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class QuestionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' =>[
                    'en' => 'Geography',
                    'am' => 'ጂኦግራፊ',
                ],
                'description' =>[
                    'en' => 'Questions related to geographical locations and features.',
                    'am' => 'ከአካባቢያዊ ቦታዎችና ባህሪያት ጋር የተያያዙ ጥያቄዎች።',
                ],
            ],
            [
                'name' =>[
                    'en' => 'History',
                    'am' => 'ታሪክ',
                ],
                'description' => [
                    'en' => 'Questions about historical events and figures.',
                    'am' => 'ስለ ታሪካዊ ክስተቶችና ሰዎች የሚያነሱ ጥያቄዎች።',
                ],
            ],
            [
                'name' => [
                    'en' => 'Science',
                    'am' => 'ሳይንስ',
                ],
                'description' =>[
                    'en' => 'Questions covering various scientific disciplines.',
                    'am' => 'በተለያዩ የሳይንስ መስኮች ላይ የሚያነሱ ጥያቄዎች።',
                ],
            ],
            [
                'name' => [
                    'en' => 'Culture',
                    'am' => 'ባህል',
                ],
                'description' =>[
                    'en' => 'Questions about cultural practices and traditions.',
                    'am' => 'ስለ ባህላዊ ልምዶችና ባህላዊ ተሞክሮዎች የሚያነሱ ጥያቄዎች።',
                ],
            ],
        ];

        foreach ($categories as $category) {
            QuestionCategory::create($category);
        }
    }
}
