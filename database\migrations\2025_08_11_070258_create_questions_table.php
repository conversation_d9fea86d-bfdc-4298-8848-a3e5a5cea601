<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('question_category_id');

            $table->foreign('question_category_id')
                ->references('id')
                ->on('question_categories')
                ->cascadeOnDelete();
            $table->tinyInteger('level')->unsigned()->default(1);
            $table->json('question_text');
            $table->json('hint')->nullable();
            $table->integer('points')->default(10);
            $table->json('answer_a');
            $table->json('answer_b');
            $table->json('answer_c');
            $table->json('answer_d');
            $table->char('correct_answer', 1);
            $table->uuid('created_by');
            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->cascadeOnDelete();
            $table->boolean('status')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
