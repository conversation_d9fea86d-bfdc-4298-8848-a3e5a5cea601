<?php

namespace App\Services;

use App\Models\Question;
use App\Models\GameSession;
use App\Models\GameSessionQuestion;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GameService
{
    public const TIME_LIMIT_SECONDS = 10;
    public const MAX_HINTS_PER_SESSION = 2;

    /**
     * Create a new game session for the user and pick one question per level 1..5
     */
    public function startForUser($user)
    {
        return DB::transaction(function () use ($user) {
            $session = GameSession::create([
                'user_id' => $user->id,
                'current_question_index' => 0,
                'score' => 0,
                'hints_used' => 0,
                'is_active' => true,
                'started_at' => now(),
            ]);

            $selected = collect();
            for ($level = 1; $level <= 5; $level++) {
                $q = Question::where('level', $level)->where('status', true)->inRandomOrder()->first();
                if (!$q) continue; // skip if no question for level
                $selected->push($q);
            }

            $selected->values()->each(function ($q, $idx) use ($session) {
                GameSessionQuestion::create([
                    'game_session_id' => $session->id,
                    'question_id' => $q->id,
                    'order_number' => $idx,
                    'question_start_time' => null,
                    'question_end_time' => null,
                    'navigation_time' => null,
                ]);
            });

            // start the first question timer
            $first = $session->questions()->orderBy('order_number')->first();
            if ($first) {
                $startTime = now();
                $first->update([
                    'question_start_time' => $startTime,
                    'question_end_time' => null,
                    'navigation_time' => null,
                    'is_paused' => false,
                ]);
            }

            return $session->fresh('questions.question.category');
        });
    }

    public function getCurrentQuestion(GameSession $session)
    {
        $idx = $session->current_question_index;
        return $session->questions()->where('order_number', $idx)->with('question.category')->first();
    }

    public function submitAnswer(GameSession $session, GameSessionQuestion $ssq, ?string $submittedAnswer,  ?int $time_taken = null)
    {
        $submittedAnswer = strtoupper($submittedAnswer ?? '');

        // Calculate how long user has been on the question
        $timeSpent = $time_taken ?? 0;
        if (!$timeSpent && $ssq->question_start_time) {
            $timeSpent = abs(now()->diffInSeconds($ssq->question_start_time));
        }

        $timedOut = $timeSpent >= GameService::TIME_LIMIT_SECONDS;

        // Decide final answer
        if ($timedOut) {
            $submittedAnswer = 'TIMEOUT'; // marker instead of null (optional)
        }

        $isCorrect = false;
        if (!$timedOut && $submittedAnswer !== '') {
            $isCorrect = $submittedAnswer === strtoupper($ssq->question->correct_answer);
        }

        // Update current question
        $ssq->update([
            'answered' => true,
            'is_correct' => $isCorrect,
            'selected_answer' => $submittedAnswer,
            'time_taken' => $timeSpent,
            'question_end_time' => now(),
            'navigation_time' => null,
            'is_paused' => true,
        ]);

        // Score only if correct and not timed out
        if ($isCorrect && !$timedOut) {
            $session->increment('score', $ssq->question->points);
        }

        // Move to next question
        $nextIndex = $session->current_question_index + 1;
        $session->update(['current_question_index' => $nextIndex]);

        $next = $session->questions()->where('order_number', $nextIndex)->first();
        if ($next) {
            $startTime = now();
            $next->update([
                'question_start_time' => $startTime,
                'question_end_time' => $startTime->copy()->addSeconds(GameService::TIME_LIMIT_SECONDS),
                'navigation_time' => null,
                'is_paused' => false,
            ]);
        } else {
            // Finished
            $session->update(['is_active' => false, 'ended_at' => now()]);
        }

        return [
            'session' => $session->fresh(),
            'next' => $next ? $next->load('question') : null,
            'correct' => $isCorrect,
            'timed_out' => $timedOut,
            'time_spent' => $timeSpent,
        ];
    }



    public function giveHint(GameSession $session, GameSessionQuestion $ssq)
    {
        $decodeField = function ($field) {
            return is_string($field) ? json_decode($field, true) : $field;
        };
        if ($session->hints_used >= self::MAX_HINTS_PER_SESSION) {
            throw new \Exception('Max hints used');
        }

        $session->increment('hints_used');
        return $decodeField($ssq->question->hint);
    }

    public function handleTimeout(GameSession $session, GameSessionQuestion $currentQuestion): array
    {
        // Mark the current question as answered but timed out
        $currentQuestion->update([
            'answered' => true,
            'selected_answer' => null, // No answer selected due to timeout
            'is_correct' => false,
            'question_end_time' => now(),
            'navigation_time' => null,
            'is_paused' => true,
        ]);

        // Find the next unanswered question by order_number
        $nextQuestion = $session->questions()
            ->where('answered', false)
            ->orderBy('order_number')
            ->first();


        if ($nextQuestion) {
            $start = now();
            $nextQuestion->update([
                'question_start_time' => $nextQuestion->question_start_time ?? $start,
                'question_end_time' => null,
                'navigation_time' => null,
                'is_paused' => false,
            ]);

            // Update session index to that question
            $session->update(['current_question_index' => $nextQuestion->order_number]);
        } else {
            // no more questions: finish session
            $session->update(['is_active' => false, 'ended_at' => now()]);
        }

        return [
            'session' => $session->fresh(), // Refresh session data
            'next' => $nextQuestion ? $nextQuestion->load('question') : null,
            'correct' => false,
        ];
    }
    public function navigateQuestion(GameSession $session, string $direction): array
    {
        $questions = $session->questions()->orderBy('order_number')->get();
        if ($questions->isEmpty()) {
            return ['question' => null, 'time_remaining' => 0];
        }

        $currentIndex = $session->current_question_index;
        $currentQuestion = $questions->firstWhere('order_number', $currentIndex);
        if (!$currentQuestion) {
            $currentQuestion = $questions->first();
            $currentIndex = $currentQuestion->order_number;
        }

        // Pause current question if active and not paused
        if ($currentQuestion->question_start_time && !$currentQuestion->is_paused) {
            // record when the user left the question
            $currentQuestion->update([
                'is_paused' => true,
                'navigation_time' => now()
            ]);
        }

        // Find the index of the current question in the collection
        $currentPos = $questions->search(fn($q) => $q->id === $currentQuestion->id);

        // Determine target position
        if ($direction === 'next') {
            $targetPos = min($currentPos + 1, $questions->count() - 1);
        } elseif ($direction === 'previous') {
            $targetPos = max($currentPos - 1, 0);
        } else {
            $targetPos = $currentPos;
        }

        $targetQuestion = $questions->get($targetPos);
        if (!$targetQuestion) {
            return ['question' => null, 'time_remaining' => 0];
        }

        // Entering target question
        if ($targetQuestion->question_start_time) {
            // If paused, resume: clear navigation_time (we'll compute elapsed from start)
            if ($targetQuestion->is_paused) {
                // resume: mark active but keep navigation_time as last-left timestamp
                $targetQuestion->update([
                    'is_paused' => false,
                ]);
            } else {
                // Already active: nothing to change
            }
        } else {
            // First visit
            $targetQuestion->update([
                'question_start_time' => now(),
                'question_end_time' => null,
                'navigation_time' => null,
                'is_paused' => false
            ]);
        }

        // Update session index to the order_number of the target question
        $session->update(['current_question_index' => $targetQuestion->order_number]);

        // Reload targetQuestion to pick up timestamp changes made above
        $targetQuestion = $session->questions()->where('order_number', $targetQuestion->order_number)->first();

        // Calculate remaining time
        $timeRemaining = $this->calculateRemainingTime($targetQuestion);

        return [
            'question' => $targetQuestion->load('question.category'),
            'time_remaining' => $timeRemaining
        ];
    }
    private function calculateRemainingTime($question)
    {
        if (!$question->question_start_time) {
            return self::TIME_LIMIT_SECONDS;
        }
        $limit = self::TIME_LIMIT_SECONDS;

        // Compute elapsed with microsecond precision.
        $start = $question->question_start_time;

        if ($question->navigation_time) {
            $nav = $question->navigation_time;
            // If navigation_time < start for any reason, fallback to now
            if ($nav->lessThan($start)) {
                $endPoint = now();
            } else {
                $endPoint = $nav;
            }
        } else {
            $endPoint = now();
        }

        // Use float seconds (U.u) for better precision
        $startFloat = (float) $start->format('U.u');
        $endFloat = (float) $endPoint->format('U.u');
        $elapsed = max(0.0, $endFloat - $startFloat);

        $remaining = $limit - $elapsed;
        return min($limit, max(0.0, round($remaining, 3)));
    }
}
