<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameSession extends Model
{
    use HasFactory, HasUuids;
    
    protected $fillable = ['user_id','current_question_index','score','hints_used','is_active','started_at','ended_at'];

    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function questions()
    {
        return $this->hasMany(GameSessionQuestion::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    
}
