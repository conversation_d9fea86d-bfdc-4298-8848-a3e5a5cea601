<?php

namespace App\Policies;

use App\Models\Question;
use App\Models\User;

class QuestionPolicy
{
    public function before(User $user, string $ability): ?bool
    {
        if ($user->hasRole('super_admin')) {
            return true;
        }
        return null;
    }

    public function viewAny(User $user): bool
    {
        return $user->hasRole('admin');
    }

    public function view(User $user, Question $question): bool
    {
        return $user->id === $question->created_by;
    }

    public function create(User $user): bool
    {
        return $user->hasRole('admin');
    }

    public function update(User $user, Question $question): bool
    {
        if ($user->hasRole('super_admin')) {
            return true;
        }


        return $user->id === $question->created_by;
    }

    public function delete(User $user, Question $question): bool
    {
        return $user->id === $question->created_by;
    }
}
