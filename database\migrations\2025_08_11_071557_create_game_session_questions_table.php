<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_session_questions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('game_session_id');
            $table->uuid('question_id');
            $table->string('selected_answer')->nullable();
            $table->tinyInteger('order_number');
            $table->boolean('answered')->default(false);
            $table->boolean('is_correct')->nullable();
            $table->timestamp('answered_at')->nullable();
            $table->timestamp('question_start_time')->nullable();
            $table->timestamp('question_end_time')->nullable();
            $table->timestamp('navigation_time')->nullable();
            $table->integer('total_time_spent')->default(0);
            $table->integer('time_taken')->default(0);
            $table->boolean('is_paused')->default(false);
            $table->timestamps();

            $table->foreign('game_session_id')
                ->references('id')->on('game_sessions')
                ->cascadeOnDelete();
            $table->foreign('question_id')
                ->references('id')->on('questions')
                ->cascadeOnDelete();
            $table->unique(['game_session_id', 'question_id'], 'unique_game_session_questions');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_session_questions');
    }
};
