<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Example data for roles
        $roles = [
            ['name' => 'super_admin'],
            ['name' => 'admin'],
            ['name' => 'user'],
            ['name' => 'guest'],
        ];

        foreach ($roles as $role) {
            \App\Models\Role::create($role);
        }
    }
}
