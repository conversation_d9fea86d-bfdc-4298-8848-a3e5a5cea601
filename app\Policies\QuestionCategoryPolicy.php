<?php

namespace App\Policies;

use App\Models\QuestionCategory;
use App\Models\User;

class QuestionCategoryPolicy
{
    public function before(User $user, string $ability): ?bool
    {
        if ($user->hasRole('super_admin')) {
            return true;
        }
        return null;
    }

    public function viewAny(User $user): bool
    {
        return $user->hasRole('super_admin');
    }

    public function view(User $user, QuestionCategory $questionCategory): bool
    {
        return $user->hasRole('super_admin');
    }

    public function create(User $user): bool
    {
        return $user->hasRole('super_admin');
    }

    public function update(User $user, QuestionCategory $questionCategory): bool
    {
        return $user->hasRole('super_admin');
    }

    public function delete(User $user, QuestionCategory $questionCategory): bool
    {
        return $user->hasRole('super_admin');
    }
}
