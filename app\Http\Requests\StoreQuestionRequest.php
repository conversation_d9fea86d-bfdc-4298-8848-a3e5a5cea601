<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = Auth::user();

        $rules = [
            'question_category_id' => 'required|exists:question_categories,id',
            'correct_answer' => 'required|in:A,B,C,D',

            'question_text' => 'required|array',
            'question_text.en' => 'nullable|string|max:1000',
            'question_text.am' => 'required_with:question_text|string|max:1000',
            'question_text.om' => 'nullable|string|max:1000',
            'question_text.ti' => 'nullable|string|max:1000',

            'answer_a' => 'required|array',
            'answer_a.en' => 'nullable|string|max:255',
            'answer_a.am' => 'required_with:answer_a|string|max:255',
            'answer_a.om' => 'nullable|string|max:255',
            'answer_a.ti' => 'nullable|string|max:255',

            'answer_b' => 'required|array',
            'answer_b.en' => 'nullable|string|max:255',
            'answer_b.am' => 'required_with:answer_b|string|max:255',
            'answer_b.om' => 'nullable|string|max:255',
            'answer_b.ti' => 'nullable|string|max:255',

            'answer_c' => 'required|array',
            'answer_c.en' => 'nullable|string|max:255',
            'answer_c.am' => 'required_with:answer_c|string|max:255',
            'answer_c.om' => 'nullable|string|max:255',
            'answer_c.ti' => 'nullable|string|max:255',

            'answer_d' => 'required|array',
            'answer_d.en' => 'nullable|string|max:255',
            'answer_d.am' => 'required_with:answer_d|string|max:255',
            'answer_d.om' => 'nullable|string|max:255',
            'answer_d.ti' => 'nullable|string|max:255',
        ];

        if ($user && $user->hasRole('super_admin')) {
            $rules['level']  = 'required|in:1,2,3,4,5';
            $rules['points'] = 'required|integer';
            $rules['hint']   = 'required|array';
            $rules['hint.en'] = 'nullable|string|max:500';
            $rules['hint.am'] = 'required|string|max:500';
            $rules['hint.om'] = 'nullable|string|max:500';
            $rules['hint.ti'] = 'nullable|string|max:500';
        } elseif ($user && $user->hasRole('admin')) {
            $rules['level']  = 'nullable|in:1,2,3,4,5';
            $rules['points'] = 'nullable|integer';
            $rules['hint']   = 'nullable|array';
            $rules['hint.en'] = 'nullable|string|max:500';
            $rules['hint.am'] = 'nullable|string|max:500';
            $rules['hint.om'] = 'nullable|string|max:500';
            $rules['hint.ti'] = 'nullable|string|max:500';
        }

        return $rules;
    }
}
