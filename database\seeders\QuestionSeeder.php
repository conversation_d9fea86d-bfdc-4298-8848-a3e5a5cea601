<?php

namespace Database\Seeders;

use App\Models\Choice;
use App\Models\Question;
use App\Models\QuestionCategory;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;

class QuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = QuestionCategory::all()->keyBy(function($cat) {
            return $cat->name['en'] ?? $cat->name;
        });
        $firstUserId = User::first()?->id;
        // Example data for questions (all translatable fields as JSON)
        $questions = [
            [
                'level' => 1,
                'category_name' => 'Geography',
                'question_text' => [
                    'en' => 'What is the capital of Ethiopia?',
                    'am' => 'የኢትዮጵያ ዋና ከተማ ማን ናት?',
                ],
                'hint' => [
                    'en' => 'It is also known as the political capital of Africa.',
                    'am' => 'እርሱ እንደ አፍሪካ ፖለቲካ ዋና ከተማ ይታወቃል።',
                ],
                'image_path' => 'images/pic1.jpg',
                'points' => 10,
                'answer_a' => [
                    'en' => 'Addis Ababa',
                    'am' => 'አዲስ አበባ',
                ],
                'answer_b' => [
                    'en' => 'Mekelle',
                    'am' => 'መቀሌ',
                ],
                'answer_c' => [
                    'en' => 'Gondar',
                    'am' => 'ጎንደር',
                ],
                'answer_d' => [
                    'en' => 'Dire Dawa',
                    'am' => 'ድሬ ዳዋ',
                ],
                'correct_answer' => 'A',
                'created_by' => $firstUserId,
            ],
            [
                'level' => 2,
                'category_name' => 'Geography',
                'question_text' => [
                    'en' => 'What is the largest city in Ethiopia?',
                    'am' => 'በኢትዮጵያ ውስጥ ትልቁ ከተማ ማን ናት?',
                ],
                'hint' => [
                    'en' => 'It is known for its historical significance and cultural heritage.',
                    'am' => 'ታሪካዊ እና ባህላዊ ዋጋ አላት።',
                ],
                'image_path' => 'images/pic2.jpg',
                'points' => 20,
                'answer_a' => [
                    'en' => 'Addis Ababa',
                    'am' => 'አዲስ አበባ',
                ],
                'answer_b' => [
                    'en' => 'Mekelle',
                    'am' => 'መቀሌ',
                ],
                'answer_c' => [
                    'en' => 'Gondar',
                    'am' => 'ጎንደር',
                ],
                'answer_d' => [
                    'en' => 'Dire Dawa',
                    'am' => 'ድሬ ዳዋ',
                ],
                'correct_answer' => 'A',
                'created_by' => $firstUserId,
            ],
            [
                'level' => 3,
                'category_name' => 'Geography',
                'question_text' => [
                    'en' => 'Which river is the longest in Ethiopia?',
                    'am' => 'በኢትዮጵያ ውስጥ በጣም ረጅም ወንዝ ማን ናት?',
                ],
                'hint' => [
                    'en' => 'It flows through the Great Rift Valley.',
                    'am' => 'በግራንድ ሪፍት ቫሊ ይፈሳል።',
                ],
                'image_path' => 'images/pic3.jpg',
                'points' => 30,
                'answer_a' => [
                    'en' => 'Blue Nile',
                    'am' => 'ዓራዲ ዓባይ',
                ],
                'answer_b' => [
                    'en' => 'Awash',
                    'am' => 'አዋሽ',
                ],
                'answer_c' => [
                    'en' => 'Omo',
                    'am' => 'ኦሞ',
                ],
                'answer_d' => [
                    'en' => 'Tekeze',
                    'am' => 'ተከዘ',
                ],
                'correct_answer' => 'A',
                'created_by' => $firstUserId,
            ],
            [
                'level' => 4,
                'category_name' => 'Science',
                'question_text' => [
                    'en' => 'What is the official language of Ethiopia?',
                    'am' => 'የኢትዮጵያ መደበኛ ቋንቋ ማን ናት?',
                ],
                'hint' => [
                    'en' => 'It is also the working language of the federal government.',
                    'am' => 'የፌዴራል መንግስት የስራ ቋንቋ ናት።',
                ],
                'image_path' => 'images/pic4.jpg',
                'points' => 40,
                'answer_a' => [
                    'en' => 'Amharic',
                    'am' => 'አማርኛ',
                ],
                'answer_b' => [
                    'en' => 'Oromo',
                    'am' => 'ኦሮሞ',
                ],
                'answer_c' => [
                    'en' => 'Tigrinya',
                    'am' => 'ትግርኛ',
                ],
                'answer_d' => [
                    'en' => 'Somali',
                    'am' => 'ሶማሌ',
                ],
                'correct_answer' => 'A',
                'created_by' => $firstUserId,
            ],
            [
                'level' => 5,
                'category_name' => 'Science',
                'question_text' => [
                    'en' => 'What is the currency of Ethiopia?',
                    'am' => 'የኢትዮጵያ ገንዘብ ምንድነው?',
                ],
                'hint' => [
                    'en' => 'It is named after a historical figure.',
                    'am' => 'ታሪካዊ ሰው ስም ይዞታል።',
                ],
                'image_path' => 'images/pic5.jpg',
                'points' => 50,
                'answer_a' => [
                    'en' => 'Birr',
                    'am' => 'ብር',
                ],
                'answer_b' => [
                    'en' => 'Dollar',
                    'am' => 'ዶላር',
                ],
                'answer_c' => [
                    'en' => 'Euro',
                    'am' => 'ዩሮ',
                ],
                'answer_d' => [
                    'en' => 'Pound',
                    'am' => 'ፓውንድ',
                ],
                'correct_answer' => 'A',
                'created_by' => $firstUserId,
            ],
            // ... add more questions as needed, all fields as arrays for translation ...
        ];

        foreach ($questions as $q) {
            $categoryId = $categories[$q['category_name']]->id ?? null;
            $question = Question::create([
                'level' => $q['level'],
                'question_category_id' => $categoryId,
                'question_text' => json_encode($q['question_text']),
                'hint' => json_encode($q['hint']),
                'points' => $q['points'],
                'answer_a' => json_encode($q['answer_a']),
                'answer_b' => json_encode($q['answer_b']),
                'answer_c' => json_encode($q['answer_c']),
                'answer_d' => json_encode($q['answer_d']),
                'correct_answer' => $q['correct_answer'],
                'created_by' => $q['created_by'],
            ]);

            if (isset($q['image_path'])) {
                $imagePath = $q['image_path'];

                // Check if file exists in storage
                if (Storage::disk('public')->exists($imagePath)) {
                    $question->addMedia(storage_path('app/public/' . $imagePath))
                        ->preservingOriginal()
                        ->toMediaCollection('questions');
                }
                // Alternatively, check if file exists in public directory (for seed images)
                elseif (file_exists(public_path($imagePath))) {
                    $question->addMedia(public_path($imagePath))
                        ->preservingOriginal()
                        ->toMediaCollection('questions');
                }
            }
        }
    }
}
