<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreQuestionCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|array',
            'name.en' => 'required_with:name|string|max:255',
            'name.am' => 'required_with:name|string|max:255',
            'name.om' => 'nullable|string|max:255',
            'name.ti' => 'nullable|string|max:255',
            'description' => 'nullable|array',
            'description.en' => 'nullable|string|max:1000',
            'description.am' => 'nullable|string|max:1000',
            'description.om' => 'nullable|string|max:1000',
            'description.ti' => 'nullable|string|max:1000',
        ];
    }
}
