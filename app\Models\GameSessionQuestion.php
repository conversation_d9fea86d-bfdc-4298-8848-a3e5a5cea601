<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameSessionQuestion extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'game_session_id',
        'question_id',
        'order_number',
        'answered',
        'is_correct',
        'answered_at',
        'question_start_time',
        'navigation_time',
        'question_end_time',
        'total_time_spent',
        'is_paused',
        'selected_answer',
        'time_taken'
    ];

    protected $casts = [
        'answered_at' => 'datetime',
        'question_start_time' => 'datetime',
        'question_start_time' => 'datetime',
        'navigation_time' => 'datetime',
        'time_taken' => 'integer',
    ];

    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function session()
    {
        return $this->belongsTo(GameSession::class, 'game_session_id');
    }
}
