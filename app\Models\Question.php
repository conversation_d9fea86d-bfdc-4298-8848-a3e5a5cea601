<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Question extends Model implements HasMedia
{
    use HasUuids, InteractsWithMedia, HasFactory;

    protected $guarded = [];

    protected $casts = [
        'question_text' => 'array',
        'hint' => 'array',
        'answer_a' => 'array',
        'answer_b' => 'array',
        'answer_c' => 'array',
        'answer_d' => 'array',
        'status' => 'boolean',
    ];

    protected $hidden = [
        'media',
        'deleted_at',
        'updated_at',
    ];
    protected $appends = ['image', 'correct_answer_text'];

    public function getCorrectAnswerTextAttribute()
    {
        return $this->{'answer_' . strtolower($this->correct_answer)};
    }


    public function getImageAttribute()
    {
        // return $this->getMedia('image')->last()?->getUrl() ?? null;
        return $this->getFirstMediaUrl('questions') ?? null;
    }

    public function category()
    {
        return $this->belongsTo(QuestionCategory::class, 'question_category_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeFilterByCategory($query, $categoryId)
    {
        return $query->where('question_category_id', $categoryId);
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($question) {
            if (Auth::check()) {
                $question->created_by = Auth::id();
            }
        });
    }
}
