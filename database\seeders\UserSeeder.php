<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Example data for users
        $super_admin = User::updateOrCreate([
            'email' => '<EMAIL>',
            'phone_number' => '251912732919',
            'username' => 'superadmin',
        ], [
            'full_name' => 'Super Admin',
            'email_verified_at' => now(),
            'status' => 1,
            'password' => bcrypt('123456')
        ]);

        $super_admin->assignRole('super_admin');

        $adminData = [
            [
                'full_name' => 'Admin One',
                'username' => 'adminone',
                'email' => '<EMAIL>',
                'phone_number' => '251900000000',
            ],
            [
                'full_name' => 'Admin Two',
                'username' => 'admintwo',
                'email' => '<EMAIL>',
                'phone_number' => '251900112233',
            ],
            [
                'full_name' => 'Admin Three',
                'username' => 'adminthree',
                'email' => '<EMAIL>',
                'phone_number' => '251911223344',
            ],

            [
                'full_name' => 'Admin Four',
                'username' => 'adminfour',
                'email' => '<EMAIL>',
                'phone_number' => '251922334455',
            ],
        ];

        foreach ($adminData as $adminData) {
            $admin = User::updateOrCreate(
                ['email' => $adminData['email']],
                [
                    'full_name' => $adminData['full_name'],
                    'username' => $adminData['username'],
                    'phone_number' => $adminData['phone_number'],
                    'email_verified_at' => now(),
                    'status' => 1,
                    'password' => Hash::make('123456'),
                ]
            );

            $admin->assignRole('admin');
        }

        $usersData = [
            [
                'full_name' => 'John Doe',
                'username' => 'johndoe',
                'email' => '<EMAIL>',
                'phone_number' => '251911111111',
            ],
            [
                'full_name' => 'Jane Smith',
                'username' => 'janesmith',
                'email' => '<EMAIL>',
                'phone_number' => '251922222222',
            ],
            [
                'full_name' => 'Michael Brown',
                'username' => 'michael',
                'email' => '<EMAIL>',
                'phone_number' => '251933333333',
            ],
            [
                'full_name' => 'Emily Davis',
                'username' => 'emily',
                'email' => '<EMAIL>',
                'phone_number' => '251944444444',
            ],
            [
                'full_name' => 'Daniel Wilson',
                'username' => 'daniel',
                'email' => '<EMAIL>',
                'phone_number' => '251955555555',
            ],
        ];

        foreach ($usersData as $userData) {
            $user = User::updateOrCreate(
                ['email' => $userData['email']],
                [
                    'full_name' => $userData['full_name'],
                    'username' => $userData['username'],
                    'phone_number' => $userData['phone_number'],
                    'email_verified_at' => now(),
                    'status' => 1,
                    'password' => Hash::make('123456'),
                ]
            );

            $user->assignRole('user');
        }
    }
}
