<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Testing\Fluent\Concerns\Has;

class QuestionCategory extends Model
{
    use HasUuids, HasFactory;
    protected $guarded = [];
    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];
    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];
    public function questions()
    {
        return $this->hasMany(Question::class);
    }
}
