<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreQuestionRequest;
use App\Http\Requests\UpdateQuestionRequest;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;

class QuestionController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index(Request $request)
    {
        Gate::authorize('viewAny', Question::class);

        $lang = $this->getLanguage($request);
        $search = trim($request->query('search', '')); // Don't convert to lowercase for non-Latin scripts

        $query = Question::query()
            ->with(['category', 'createdBy'])
            ->latest();

        // Limit non-super_admin users
        if (!Auth::user()->hasRole('super_admin')) {
            $query->where('created_by', Auth::id());
        }

        if ($request->filled('search')) {
            $driver = DB::getDriverName();
            $like = "%{$search}%";

            $query->where(function ($q) use ($driver, $lang, $like, $search) {
                $fields = ['question_text', 'hint', 'answer_a', 'answer_b', 'answer_c', 'answer_d'];

                if ($driver === 'pgsql') {
                    // PostgreSQL: Search across all languages in JSON fields
                    foreach ($fields as $col) {
                        $q->orWhereRaw("
                            ($col->>? ILIKE ? OR $col->>'en' ILIKE ? OR $col::text ILIKE ?)
                        ", [$lang, $like, $like, $like]);
                    }

                    $q->orWhereHas('category', function ($cat) use ($lang, $like) {
                        $cat->whereRaw("
                            (name->>? ILIKE ? OR name->>'en' ILIKE ? OR name::text ILIKE ?)
                        ", [$lang, $like, $like, $like]);
                    });
                } else {
                    // MySQL: Search across all languages in JSON fields
                    foreach ($fields as $col) {
                        $q->orWhereRaw("
                            (JSON_UNQUOTE(JSON_EXTRACT($col, '$.\"{$lang}\"')) LIKE ?
                             OR JSON_UNQUOTE(JSON_EXTRACT($col, '$.\"en\"')) LIKE ?
                             OR $col LIKE ?)
                        ", [$like, $like, $like]);
                    }

                    $q->orWhereHas('category', function ($cat) use ($lang, $like) {
                        $cat->whereRaw("
                            (JSON_UNQUOTE(JSON_EXTRACT(name, '$.\"{$lang}\"')) LIKE ?
                             OR JSON_UNQUOTE(JSON_EXTRACT(name, '$.\"en\"')) LIKE ?
                             OR name LIKE ?)
                        ", [$like, $like, $like]);
                    });
                }

                // Level search
                if (is_numeric($search)) {
                    $q->orWhere('level', intval($search));
                }
            });
        }

        $perPage = (int) $request->query('per_page', 10);

        return $query->paginate($perPage)->through(function ($question) use ($lang) {
            return $this->formatQuestion($question, $lang);
        });
    }






    public function store(StoreQuestionRequest $request)
    {
        Gate::authorize('create', Question::class);

        try {
            DB::beginTransaction();

            $question = Question::create($request->validated());

            if ($request->hasFile('image')) {
                $question->addMediaFromRequest('image')->toMediaCollection('questions');
            }

            DB::commit();

            return response()->json($this->formatQuestion($question->fresh(), $this->getLanguage($request)), 201);
        } catch (ValidationException $e) {
            return response()->json(['error' => 'Validation failed', 'messages' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'An error occurred while creating the question'], 400);
        }
    }

    public function show(Request $request, string $id)
    {

        //$lang = $this->getLanguage($request);

        $question = Question::with(['category', 'createdBy'])->findOrFail($id);
        Gate::authorize('view', $question);
        return response()->json($this->formatQuestionShow($question));
    }

    public function update(UpdateQuestionRequest $request, string $id)
    {
        $question = Question::findOrFail($id);
        Gate::authorize('update', $question);

        try {
            $validated = $request->validated();
            $data = [];


            foreach (['question_text', 'hint', 'answer_a', 'answer_b', 'answer_c', 'answer_d'] as $field) {
                if (array_key_exists($field, $validated)) {
                    $data[$field] = json_encode($validated[$field]);
                }
            }


            foreach (['question_category_id', 'correct_answer', 'level', 'points', 'status'] as $field) {
                if (array_key_exists($field, $validated)) {
                    $data[$field] = $validated[$field];
                }
            }

            $question->update($data);

            if ($request->hasFile('image')) {
                $question->clearMediaCollection('questions');
                $question->addMediaFromRequest('image')->toMediaCollection('questions');
            }

            return response()->json($this->formatQuestion($question->fresh(), $this->getLanguage($request)), 200);
        } catch (ValidationException $e) {
            return response()->json(['error' => 'Validation failed', 'messages' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => 'An error occurred while updating the question'], 400);
        }
    }

    public function destroy(string $id)
    {
        $question = Question::findOrFail($id);
        Gate::authorize('delete', Question::class);
        $question->delete();
        return response()->json(['message' => 'Question deleted successfully'], 200);
    }

    /**
     * Format a question for API response, applying language filter.
     */
    protected function formatQuestion($question, ?string $lang = 'en')
    {
        return [
            'id' => $question->id,
            'category' => $this->pickLang($question->category?->name, $lang),
            'category_id' => $question->category?->id,
            'question_text' => $this->pickLang($this->decodeJsonField($question->question_text), $lang),
            'hint' => $this->pickLang($this->decodeJsonField($question->hint), $lang),
            'level' => $question->level,
            'points' => $question->points,
            'status' => $question->status ? 'Active' : 'Inactive',
            'answer_a' => $this->pickLang($this->decodeJsonField($question->answer_a), $lang),
            'answer_b' => $this->pickLang($this->decodeJsonField($question->answer_b), $lang),
            'answer_c' => $this->pickLang($this->decodeJsonField($question->answer_c), $lang),
            'answer_d' => $this->pickLang($this->decodeJsonField($question->answer_d), $lang),
            'correct_answer' => $question->correct_answer,
            'image' => $question->image,
            'created_by' => $question->created_by,
            'created_by_name' => $question->createdBy?->full_name,
            'created_at' => $question->created_at,
        ];
    }


    protected function formatQuestionShow($question)
    {
        return [
            'id' => $question->id,
            'category' => $this->decodeJsonField($question->category?->name),
            'category_id' => $question->category?->id,
            'question_text' => $this->decodeJsonField($question->question_text),
            'hint' => $this->decodeJsonField($question->hint),
            'level' => $question->level,
            'points' => $question->points,
            'status' => $question->status ? 'Active' : 'Inactive',
            'answer_a' => $this->decodeJsonField($question->answer_a),
            'answer_b' => $this->decodeJsonField($question->answer_b),
            'answer_c' => $this->decodeJsonField($question->answer_c),
            'answer_d' => $this->decodeJsonField($question->answer_d),
            'correct_answer' => $question->correct_answer,
            'image' => $question->image,
            'created_by' => $question->created_by,
            'created_by_name' => $question->createdBy?->full_name,
            'created_at' => $question->created_at,
        ];
    }

    /**
     * Decode a JSON field.
     */
    protected function decodeJsonField($value)
    {
        if (is_array($value)) return $value;
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded === null ? $value : $decoded;
        }
        return $value;
    }

    /**
     * Pick the requested language from a JSON field, default to 'en'.
     */
    private function pickLang($field, ?string $lang = 'en')
    {
        if (is_string($field)) {
            $field = json_decode($field, true);
        }
        if (is_array($field) && $lang && isset($field[$lang])) {
            return $field[$lang];
        }
        return is_array($field) ? reset($field) : $field;
    }

    /**
     * Get language from query parameters, default 'en'.
     */
    private function getLanguage(Request $request): string
    {
        return $request->query('lang', 'en');
    }
}
