<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreQuestionCategoryRequest;
use App\Http\Requests\UpdateQuestionCategoryRequest;
use App\Models\QuestionCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\ValidationException;

class QuestionCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Gate::authorize('viewAny', QuestionCategory::class);

        $lang = $this->getLanguage($request);

        $categories = QuestionCategory::latest()->paginate(10);
        $categories->getCollection()->transform(function ($category) use ($lang) {
            return $this->formatCategory($category, $lang);
        });

        return response()->json($categories, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreQuestionCategoryRequest $request)
    {
        try {
            Gate::authorize('create', QuestionCategory::class);
            DB::beginTransaction();

            $category = QuestionCategory::create([
                'name' => $request->name,           // JSON input expected
                'description' => $request->description, // JSON input optional
            ]);

            DB::commit();
            return response()->json($this->formatCategory($category, $this->getLanguage(request())), 201);
        } catch (ValidationException $e) {
            return response()->json(['error' => 'Validation failed', 'messages' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'An error occurred while creating the category'], 400);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        //Gate::authorize('view', QuestionCategory::class);
        $lang = $this->getLanguage($request);

        $category = QuestionCategory::findOrFail($id);
        return response()->json($this->formatCategory($category, $lang));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateQuestionCategoryRequest $request, string $id)
    {
        try {
            Gate::authorize('update', QuestionCategory::class);
            DB::beginTransaction();

            $category = QuestionCategory::findOrFail($id);
            $category->update($request->only(['name', 'description']));

            DB::commit();
            return response()->json($this->formatCategory($category, $this->getLanguage(request())), 200);
        } catch (ValidationException $e) {
            return response()->json(['error' => 'Validation failed', 'messages' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'An error occurred while updating the category'], 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        Gate::authorize('delete', QuestionCategory::class);
        $category = QuestionCategory::findOrFail($id);
        $category->delete();

        return response()->json(['message' => 'Category deleted successfully'], 200);
    }

    /**
     * Format a category for API response, applying language filter.
     */
    protected function formatCategory($category, ?string $lang = 'en')
    {
        return [
            'id' => $category->id,
            'name' => $this->pickLang($category->name, $lang),
            'description' => $this->pickLang($category->description, $lang),
        ];
    }

    /**
     * Pick a specific language from JSON field, default to 'en'.
     */
    private function pickLang($field, ?string $lang = 'en')
    {
        if (is_string($field)) {
            $field = json_decode($field, true);
        }
        if (is_array($field) && isset($field[$lang])) {
            return $field[$lang];
        }
        return is_array($field) ? reset($field) : $field;
    }

    /**
     * Get the requested language from query string, default to 'en'.
     */
    private function getLanguage(Request $request): string
    {
        return $request->query('lang', 'en');
    }
}
